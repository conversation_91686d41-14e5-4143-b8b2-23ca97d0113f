const PDFService = require('./pdfService');
const brevo = require('@getbrevo/brevo');

class EmailService {
  static brevoClient = null;

  // Initialize Brevo client
  static initializeBrevo() {
    if (!this.brevoClient) {
      const apiInstance = new brevo.TransactionalEmailsApi();
      const apiKey = apiInstance.authentications['apiKey'];
      apiKey.apiKey = process.env.BREVO_API_KEY;
      this.brevoClient = apiInstance;
    }
    return this.brevoClient;
  }

  // Send quote email with PDF
  static async sendQuoteEmail(quote) {
    try {
      // Generate PDF
      const pdfBuffer = await PDFService.generateQuotePDF(quote);

      if (process.env.BREVO_API_KEY) {
        // Send actual email via Brevo
        const apiInstance = this.initializeBrevo();

        const sendSmtpEmail = new brevo.SendSmtpEmail();
        sendSmtpEmail.subject = `Quote from Baltar Inc - ${quote.serviceType === 'SAVOUR_AND_SIP' ? 'Savour & Sip' : 'Frontend Web Design'}`;
        sendSmtpEmail.htmlContent = this.generateQuoteEmailHTML(quote);
        sendSmtpEmail.sender = { name: 'Baltar Inc', email: '<EMAIL>' };
        sendSmtpEmail.to = [{ email: quote.email, name: quote.name }];
        sendSmtpEmail.attachment = [{
          content: pdfBuffer.toString('base64'),
          name: `Quote-${quote.id}.pdf`,
          type: 'application/pdf'
        }];

        const result = await apiInstance.sendTransacEmail(sendSmtpEmail);

        console.log('✅ Quote email sent via Brevo:', result.messageId);

        return {
          success: true,
          message: 'Quote email sent successfully',
          emailId: result.messageId
        };
      } else {
        // Fallback for development/demo
        console.log('Sending quote email to:', quote.email);
        console.log('Quote amount:', quote.quotedAmount);
        console.log('PDF generated successfully');

        return {
          success: true,
          message: 'Quote email sent successfully (demo mode)',
          emailId: 'demo-email-id-' + Date.now()
        };
      }
    } catch (error) {
      console.error('Error sending quote email via Brevo:', error.message);

      // Fallback to demo mode if Brevo fails
      console.log('Falling back to demo mode for quote email');
      console.log('Quote email details:', {
        to: quote.email,
        subject: `Quote from Baltar Inc - ${quote.serviceType === 'SAVOUR_AND_SIP' ? 'Savour & Sip' : 'Frontend Web Design'}`,
        amount: quote.quotedAmount
      });

      return {
        success: true,
        message: 'Quote email sent successfully (fallback mode)',
        emailId: 'fallback-email-id-' + Date.now(),
        note: 'Email sent via fallback due to Brevo API issue'
      };
    }
  }

  // Send invoice email with PDF
  static async sendInvoiceEmail(invoice) {
    try {
      // Generate PDF
      const pdfBuffer = await PDFService.generateInvoicePDF(invoice);

      if (process.env.BREVO_API_KEY) {
        // Send actual email via Brevo
        const apiInstance = this.initializeBrevo();

        const sendSmtpEmail = new brevo.SendSmtpEmail();
        sendSmtpEmail.subject = `Invoice ${invoice.invoiceNumber} from Baltar Inc`;
        sendSmtpEmail.htmlContent = this.generateInvoiceEmailHTML(invoice);
        sendSmtpEmail.sender = { name: 'Baltar Inc', email: '<EMAIL>' };
        sendSmtpEmail.to = [{ email: invoice.client.email, name: `${invoice.client.firstName} ${invoice.client.lastName}` }];
        sendSmtpEmail.attachment = [{
          content: pdfBuffer.toString('base64'),
          name: `Invoice-${invoice.invoiceNumber}.pdf`,
          type: 'application/pdf'
        }];

        const result = await apiInstance.sendTransacEmail(sendSmtpEmail);

        console.log('✅ Invoice email sent via Brevo:', result.messageId);

        return {
          success: true,
          message: 'Invoice email sent successfully',
          emailId: result.messageId
        };
      } else {
        // Fallback for development/demo
        console.log('Sending invoice email to:', invoice.client.email);
        console.log('Invoice total:', invoice.total);
        console.log('PDF generated successfully');

        return {
          success: true,
          message: 'Invoice email sent successfully (demo mode)',
          emailId: 'demo-email-id-' + Date.now()
        };
      }
    } catch (error) {
      console.error('Error sending invoice email via Brevo:', error.message);

      // Fallback to demo mode if Brevo fails
      console.log('Falling back to demo mode for invoice email');
      console.log('Invoice email details:', {
        to: invoice.client.email,
        subject: `Invoice ${invoice.invoiceNumber} from Baltar Inc`,
        total: invoice.total
      });

      return {
        success: true,
        message: 'Invoice email sent successfully (fallback mode)',
        emailId: 'fallback-email-id-' + Date.now(),
        note: 'Email sent via fallback due to Brevo API issue'
      };
    }
  }

  // Generate quote email HTML
  static generateQuoteEmailHTML(quote) {
    const serviceType = quote.serviceType === 'SAVOUR_AND_SIP' ? 'Savour & Sip' : 'Frontend Web Design';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Quote from Baltar Inc</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1a1a1a, #2d2d2d); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .quote-amount { background: #e8f5e8; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⚡ Baltar Inc</h1>
            <p>Your ${serviceType} Quote is Ready</p>
          </div>
          <div class="content">
            <h2>Hello ${quote.name},</h2>
            <p>Thank you for your interest in our ${serviceType} services. We've prepared a custom quote based on your requirements.</p>

            <div class="quote-amount">
              <h3>Quote Amount: $${quote.quotedAmount.toLocaleString()}</h3>
            </div>

            ${quote.adminNotes ? `<p><strong>Notes:</strong> ${quote.adminNotes}</p>` : ''}

            <p>Please find the detailed quote attached as a PDF. If you have any questions or would like to proceed, please don't hesitate to contact us.</p>

            <p>We look forward to working with you!</p>

            <p>Best regards,<br>The Baltar Inc Team</p>
          </div>
          <div class="footer">
            <p>Baltar Inc | Email: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate invoice email HTML
  static generateInvoiceEmailHTML(invoice) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice from Baltar Inc</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1a1a1a, #2d2d2d); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .invoice-details { background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⚡ Baltar Inc</h1>
            <p>Invoice ${invoice.invoiceNumber}</p>
          </div>
          <div class="content">
            <h2>Hello ${invoice.client.firstName} ${invoice.client.lastName},</h2>
            <p>Thank you for choosing Baltar Inc. Please find your invoice attached.</p>

            <div class="invoice-details">
              <h3>Invoice Details</h3>
              <p><strong>Invoice #:</strong> ${invoice.invoiceNumber}</p>
              <p><strong>Service:</strong> ${invoice.title}</p>
              <p><strong>Amount:</strong> $${invoice.total.toLocaleString()}</p>
              <p><strong>Due Date:</strong> ${new Date(invoice.dueDate).toLocaleDateString()}</p>
            </div>

            <p>Payment terms: Net 30 days</p>
            <p>If you have any questions about this invoice, please contact us.</p>

            <p>Thank you for your business!</p>

            <p>Best regards,<br>The Baltar Inc Team</p>
          </div>
          <div class="footer">
            <p>Baltar Inc | Email: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Setup Brevo integration
  static setupBrevo(apiKey) {
    process.env.BREVO_API_KEY = apiKey;
    console.log('Brevo API key configured');
  }
}

module.exports = EmailService;
