const PDFService = require('./pdfService');

class EmailService {
  // Send quote email with PDF
  static async sendQuoteEmail(quote) {
    try {
      // Generate PDF
      const pdfBuffer = await PDFService.generateQuotePDF(quote);
      
      // TODO: Implement Brevo email sending
      // For now, just log the email details
      console.log('Sending quote email to:', quote.email);
      console.log('Quote amount:', quote.quotedAmount);
      console.log('PDF generated successfully');

      // Simulate email sending
      return {
        success: true,
        message: 'Quote email sent successfully',
        emailId: 'mock-email-id-' + Date.now()
      };
    } catch (error) {
      throw new Error('Failed to send quote email: ' + error.message);
    }
  }

  // Send invoice email with PDF
  static async sendInvoiceEmail(invoice) {
    try {
      // Generate PDF
      const pdfBuffer = await PDFService.generateInvoicePDF(invoice);
      
      // TODO: Implement Brevo email sending
      console.log('Sending invoice email to:', invoice.client.email);
      console.log('Invoice total:', invoice.total);
      console.log('PDF generated successfully');

      // Simulate email sending
      return {
        success: true,
        message: 'Invoice email sent successfully',
        emailId: 'mock-email-id-' + Date.now()
      };
    } catch (error) {
      throw new Error('Failed to send invoice email: ' + error.message);
    }
  }

  // Setup Brevo integration (to be implemented)
  static setupBrevo(apiKey) {
    // TODO: Initialize Brevo client
    console.log('Brevo API key configured');
  }
}

module.exports = EmailService;
