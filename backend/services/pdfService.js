const PDFDocument = require('pdfkit');

class PDFService {
  // Generate quote PDF
  static async generateQuotePDF(quote) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument();
        const chunks = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Header
        doc.fontSize(24).text('Baltar Inc', 50, 50);
        doc.fontSize(16).text('Professional Services Quote', 50, 80);
        
        // Quote details
        doc.fontSize(12);
        doc.text(`Quote ID: ${quote.id}`, 50, 120);
        doc.text(`Date: ${new Date(quote.createdAt).toLocaleDateString()}`, 50, 140);
        
        // Client information
        doc.fontSize(14).text('Client Information:', 50, 180);
        doc.fontSize(12);
        doc.text(`Name: ${quote.name}`, 50, 200);
        doc.text(`Email: ${quote.email}`, 50, 220);
        if (quote.phone) doc.text(`Phone: ${quote.phone}`, 50, 240);
        
        // Service details
        let yPosition = 280;
        doc.fontSize(14).text('Service Details:', 50, yPosition);
        yPosition += 20;
        
        if (quote.serviceType === 'SAVOUR_AND_SIP') {
          doc.fontSize(12);
          doc.text(`Service Type: Savour & Sip Catering`, 50, yPosition);
          yPosition += 20;
          if (quote.eventType) {
            doc.text(`Event Type: ${quote.eventType}`, 50, yPosition);
            yPosition += 20;
          }
          if (quote.guestCount) {
            doc.text(`Guest Count: ${quote.guestCount}`, 50, yPosition);
            yPosition += 20;
          }
          if (quote.eventDate) {
            doc.text(`Event Date: ${new Date(quote.eventDate).toLocaleDateString()}`, 50, yPosition);
            yPosition += 20;
          }
          if (quote.services) {
            const services = JSON.parse(quote.services);
            doc.text(`Services: ${services.join(', ')}`, 50, yPosition);
            yPosition += 20;
          }
        } else if (quote.serviceType === 'FRONTEND_WEB_DESIGN') {
          doc.fontSize(12);
          doc.text(`Service Type: Frontend Web Design`, 50, yPosition);
          yPosition += 20;
          if (quote.company) {
            doc.text(`Company: ${quote.company}`, 50, yPosition);
            yPosition += 20;
          }
          if (quote.websiteType) {
            doc.text(`Website Type: ${quote.websiteType}`, 50, yPosition);
            yPosition += 20;
          }
          if (quote.budget) {
            doc.text(`Budget Range: ${quote.budget}`, 50, yPosition);
            yPosition += 20;
          }
        }
        
        if (quote.message) {
          yPosition += 20;
          doc.text(`Additional Details: ${quote.message}`, 50, yPosition, { width: 500 });
          yPosition += 60;
        }
        
        // Quote amount
        yPosition += 20;
        doc.fontSize(16).text('Quote Amount:', 50, yPosition);
        doc.fontSize(20).text(`$${quote.quotedAmount.toLocaleString()}`, 50, yPosition + 25);
        
        // Admin notes
        if (quote.adminNotes) {
          yPosition += 80;
          doc.fontSize(14).text('Notes:', 50, yPosition);
          doc.fontSize(12).text(quote.adminNotes, 50, yPosition + 20, { width: 500 });
        }
        
        // Footer
        doc.fontSize(10).text('Thank you for choosing Baltar Inc!', 50, doc.page.height - 100);
        doc.text('Contact <NAME_EMAIL> for any questions.', 50, doc.page.height - 80);
        
        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  // Generate invoice PDF
  static async generateInvoicePDF(invoice) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument();
        const chunks = [];

        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Header
        doc.fontSize(24).text('Baltar Inc', 50, 50);
        doc.fontSize(16).text('Invoice', 50, 80);
        
        // Invoice details
        doc.fontSize(12);
        doc.text(`Invoice #: ${invoice.invoiceNumber}`, 50, 120);
        doc.text(`Date: ${new Date(invoice.issueDate).toLocaleDateString()}`, 50, 140);
        doc.text(`Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}`, 50, 160);
        
        // Client information
        doc.fontSize(14).text('Bill To:', 50, 200);
        doc.fontSize(12);
        doc.text(`${invoice.client.firstName} ${invoice.client.lastName}`, 50, 220);
        doc.text(`${invoice.client.email}`, 50, 240);
        if (invoice.client.phone) doc.text(`${invoice.client.phone}`, 50, 260);
        
        // Invoice items
        let yPosition = 320;
        doc.fontSize(14).text('Description:', 50, yPosition);
        doc.text('Amount:', 400, yPosition);
        yPosition += 30;
        
        // Draw line
        doc.moveTo(50, yPosition).lineTo(550, yPosition).stroke();
        yPosition += 20;
        
        doc.fontSize(12);
        doc.text(invoice.title, 50, yPosition);
        doc.text(`$${invoice.subtotal.toLocaleString()}`, 400, yPosition);
        yPosition += 40;
        
        if (invoice.description) {
          doc.text(invoice.description, 50, yPosition, { width: 300 });
          yPosition += 60;
        }
        
        // Totals
        doc.moveTo(350, yPosition).lineTo(550, yPosition).stroke();
        yPosition += 20;
        
        doc.text('Subtotal:', 350, yPosition);
        doc.text(`$${invoice.subtotal.toLocaleString()}`, 450, yPosition);
        yPosition += 20;
        
        if (invoice.tax > 0) {
          doc.text('Tax:', 350, yPosition);
          doc.text(`$${invoice.tax.toLocaleString()}`, 450, yPosition);
          yPosition += 20;
        }
        
        doc.fontSize(14).text('Total:', 350, yPosition);
        doc.text(`$${invoice.total.toLocaleString()}`, 450, yPosition);
        
        // Footer
        doc.fontSize(10).text('Thank you for your business!', 50, doc.page.height - 100);
        doc.text('Payment terms: Net 30 days', 50, doc.page.height - 80);
        
        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }
}

module.exports = PDFService;
